//
//  IMYSGAnalysisBannerSDK.m
//  IMYBaseKit
//
//  Created by ljh on 2025/9/1.
//

#import "IMYSGAnalysisBannerSDK.h"
#import "IMYBaseKit.h"

/// 分析会员横幅-范围值
@interface IMYSGAnalysisBannerConfigRange : NSObject
/// 最小值
@property (nonatomic, copy) NSString *min;
/// 最大值
@property (nonatomic, copy) NSString *max;
@end

/// 分析会员横幅-具体配置
@interface IMYSGAnalysisBannerConfigRemote ()
/// 素材ID
@property (nonatomic, assign) NSInteger vip_material_id;
/// 文本素材内容列表
@property (nonatomic, copy) NSArray<NSString *> *text_material;
/// 跳转协议
@property (nonatomic, copy) NSString *uri;
/// 记录项ID
@property (nonatomic, assign) NSInteger item;
/// 周期：1=经期周期阶段（经期身份） 2=备孕经期周期阶段（备孕身份）3=宝宝月龄（育儿身份）4=孕周阶段（怀孕身份），按数字查具体条件字段
@property (nonatomic, assign) NSInteger period_type;
/// 经期周期阶段1
@property (nonatomic, copy) NSArray<NSNumber *> *period_stage;
///备孕经期周期阶段2
@property (nonatomic, copy) NSArray<NSNumber *> *prepare_preg_stage;
/// 宝宝月龄范围
@property (nonatomic, strong) IMYSGAnalysisBannerConfigRange *baby_month;
/// 时间维度：1本周期，2近半年
@property (nonatomic, assign) NSInteger time_range;
/// 区间配置：爱爱次数、体温
@property (nonatomic, copy) NSArray<IMYSGAnalysisBannerConfigRange *> *ranges;
/// 分析结果
@property (nonatomic, assign) NSInteger analysis_result;
/// 记录子项
@property (nonatomic, copy) NSArray<NSNumber *> *sub_items;
/// 资源位标识：分析列表会员引导入口（ANALYSIS_VIP_GUIDE），分析详情页会员引导入口（ANALYSIS_DETAIL_VIP_GUIDE）
@property (nonatomic, copy) NSString *location_key;

/// 返回业务显示用的 UI配置
- (IMYSGAnalysisBannerUIConfig *)getUIConfig;

@end

static inline NSString * getLocationKey(IMYSGAnalysisBannerPageIn const pageIn) {
    switch (pageIn) {
        case IMYSGAnalysisBannerPageInList:
            return @"ANALYSIS_VIP_GUIDE";
        case IMYSGAnalysisBannerPageInDetail:
            return @"ANALYSIS_DETAIL_VIP_GUIDE";
    }
    return nil;
}

#pragma mark - SDK

@interface IMYSGAnalysisBannerSDK ()
@property (atomic, copy) NSArray<IMYSGAnalysisBannerConfigRemote *> *currentRecordConfigs;
@end

@implementation IMYSGAnalysisBannerSDK

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [self new];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _loadedSignal = [RACReplaySubject replaySubjectWithCapacity:1];
    }
    return self;
}

- (void)updateAllConfigsWithArray:(NSArray<IMYSGAnalysisBannerConfigRemote *> * const)allConfigs {
    // 延迟释放旧对象
    id const holds = self.currentRecordConfigs;
    // 记录项目相关 banner
    self.currentRecordConfigs = allConfigs;
    // 1秒后释放
    imy_asyncMainBlock(1, ^{
        [holds class];
    });
    // 更新通知
    [self postNotification];
}

- (void)postNotification {
    imy_asyncMainBlock(^{
        [((RACSubject *)self.loadedSignal) sendNext:self];
    });
}

/// 周期天数、周期规律性、经期天数、流量变化、痛经情况、症状程度、症状数量
/// 分析结果
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger const)itemId
                                         pageIn:(IMYSGAnalysisBannerPageIn const)pageIn
                                analysisResults:(NSArray<IMYSGAnalysisAPIResults *> * const)analysisResults {
    if (itemId < 101 || itemId > 107) {
        NSAssert(NO, @"item id 不匹配");
        return nil;
    }
    NSString * const pageInKey = getLocationKey(pageIn);
    // 寻找第一个满足条件的配置项
    IMYSGAnalysisBannerConfigRemote *hitConfig = [self.currentRecordConfigs bk_match:^BOOL(IMYSGAnalysisBannerConfigRemote *config) {
        // 满足多模块id配置
        BOOL const isHitItem = (config.item == itemId || [analysisResults bk_any:^BOOL(IMYSGAnalysisAPIResults *result) {
            return config.item == result.item_id;
        }]);
        if (!isHitItem) {
            // 模块id没匹配
            return NO;
        }
        // 页面位置没通过
        if (pageInKey.length > 0 && config.location_key.length > 0) {
            if (![pageInKey isEqualToString:config.location_key]) {
                return NO;
            }
        }
        // 服务端没配置周期和分析结果，就不用进行匹配，直接返回该配置
        if (config.time_range == 0 && config.analysis_result == 0) {
            return YES;
        }
        // 配置周期和分析结果参数
        return [analysisResults bk_any:^BOOL(IMYSGAnalysisAPIResults *result) {
            return (config.time_range == result.time_range && config.analysis_result == result.result_id);
        }];
    }];
    
#ifdef DEBUG
    // 上报给测试验证
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"request"] = @{
        @"itemId" : @(itemId),
        @"pageIn" : @(pageIn),
        @"analysisResults" : ([analysisResults map:^id _Nonnull(IMYSGAnalysisAPIResults *element) {
            return @{
                @"item_id" : @(element.item_id),
                @"time_range" : @(element.time_range),
                @"result_id" : @(element.result_id),
            };
        }] ?: @[]),
    };
    params[@"return"] = [hitConfig yy_modelToJSONObject];
    [[IMYPublicServerRequest postPath:@"qa-test/analysis_sdk.do" host:@"http://qa-test.seeyouyima.com" params:params headers:nil] subscribeCompleted:^{
        //...
    }];
#endif
    
    // 返回UI素材配置
    return [hitConfig getUIConfig];
}

/// 爱爱分析
/// 备孕各阶段时期，爱爱次数
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger const)itemId
                                         pageIn:(IMYSGAnalysisBannerPageIn const)pageIn
                                      pregStage:(NSInteger const)preparePregStage
                                      loveCount:(NSInteger const)loveCount {
    if (itemId != 2) {
        NSAssert(NO, @"item id 不匹配");
        return nil;
    }
    NSString * const pageInKey = getLocationKey(pageIn);
    // 寻找第一个满足条件的配置项
    IMYSGAnalysisBannerConfigRemote *hitConfig = [self.currentRecordConfigs bk_match:^BOOL(IMYSGAnalysisBannerConfigRemote *config) {
        // 模块id配置
        if (config.item != itemId) {
            return NO;
        }
        // 页面位置没通过
        if (pageInKey.length > 0 && config.location_key.length > 0) {
            if (![pageInKey isEqualToString:config.location_key]) {
                return NO;
            }
        }
        // 判断是否需要匹配备孕周期
        if (config.prepare_preg_stage.count > 0 && ![config.prepare_preg_stage containsObject:@(preparePregStage)]) {
            return NO;
        }
        // 服务端没配置爱爱区间，则不用匹配，直接返回该配置项
        if (!config.ranges.count) {
            return YES;
        }
        // 匹配爱爱次数区间
        return [config.ranges bk_any:^BOOL(IMYSGAnalysisBannerConfigRange *range) {
            if (range.min.length > 0 && range.min.integerValue > loveCount) {
                // 有最小值限定，业务传的值不满足
                return NO;
            }
            if (range.max.length > 0 && range.max.integerValue < loveCount) {
                // 有最大值限定，业务传的值不满足
                return NO;
            }
            return YES;
        }];
    }];
    
#ifdef DEBUG
    // 上报给测试验证
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"request"] = @{
        @"itemId" : @(itemId),
        @"pageIn" : @(pageIn),
        @"preparePregStage" : @(preparePregStage),
        @"loveCount" : @(loveCount),
    };
    params[@"return"] = [hitConfig yy_modelToJSONObject];
    [[IMYPublicServerRequest postPath:@"qa-test/analysis_sdk.do" host:@"http://qa-test.seeyouyima.com" params:params headers:nil] subscribeCompleted:^{
        //...
    }];
#endif
    
    // 返回UI素材配置
    return [hitConfig getUIConfig];
}

/// 体温分析
/// 备孕各阶段时期，体温区间
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger const)itemId
                                         pageIn:(IMYSGAnalysisBannerPageIn const)pageIn
                                      pregStage:(NSInteger const)preparePregStage
                                    temperature:(double const)temperature {
    if (itemId != 11) {
        NSAssert(NO, @"item id 不匹配");
        return nil;
    }
    NSString * const pageInKey = getLocationKey(pageIn);
    // 寻找第一个满足条件的配置项
    IMYSGAnalysisBannerConfigRemote *hitConfig = [self.currentRecordConfigs bk_match:^BOOL(IMYSGAnalysisBannerConfigRemote *config) {
        // 模块id配置
        if (config.item != itemId) {
            return NO;
        }
        // 页面位置没通过
        if (pageInKey.length > 0 && config.location_key.length > 0) {
            if (![pageInKey isEqualToString:config.location_key]) {
                return NO;
            }
        }
        // 判断是否需要匹配备孕周期
        if (config.prepare_preg_stage.count > 0 && ![config.prepare_preg_stage containsObject:@(preparePregStage)]) {
            return NO;
        }
        // 服务端没配置体温区间，则不用匹配，直接返回该配置项
        if (!config.ranges.count) {
            return YES;
        }
        // 体温在区间内
        return [config.ranges bk_any:^BOOL(IMYSGAnalysisBannerConfigRange *range) {
            if (range.min.length > 0 && range.min.doubleValue > temperature) {
                // 有最小值限定，业务传的值不满足
                return NO;
            }
            if (range.max.length > 0 && range.max.doubleValue < temperature) {
                // 有最大值限定，业务传的值不满足
                return NO;
            }
            return YES;
        }];
    }];
    
#ifdef DEBUG
    // 上报给测试验证
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"request"] = @{
        @"itemId" : @(itemId),
        @"pageIn" : @(pageIn),
        @"preparePregStage" : @(preparePregStage),
        @"temperature" : @(temperature),
    };
    params[@"return"] = [hitConfig yy_modelToJSONObject];
    [[IMYPublicServerRequest postPath:@"qa-test/analysis_sdk.do" host:@"http://qa-test.seeyouyima.com" params:params headers:nil] subscribeCompleted:^{
        //...
    }];
#endif
    
    // 返回UI素材配置
    return [hitConfig getUIConfig];
}

/// 白带分析、体重
/// 备孕各阶段时期，当天最新的白带记录、最新一次记录体重的BMI范围
- (IMYSGAnalysisBannerUIConfig *)configsForItem:(NSInteger const)itemId
                                         pageIn:(IMYSGAnalysisBannerPageIn const)pageIn
                                      pregStage:(NSInteger const)preparePregStage
                                        subItem:(NSInteger const)subItem {
    if (itemId != 13 && itemId != 4) {
        NSAssert(NO, @"item id 不匹配");
        return nil;
    }
    NSString * const pageInKey = getLocationKey(pageIn);
    // 寻找第一个满足条件的配置项
    IMYSGAnalysisBannerConfigRemote *hitConfig = [self.currentRecordConfigs bk_match:^BOOL(IMYSGAnalysisBannerConfigRemote *config) {
        // 模块id配置
        if (config.item != itemId) {
            return NO;
        }
        // 页面位置没通过
        if (pageInKey.length > 0 && config.location_key.length > 0) {
            if (![pageInKey isEqualToString:config.location_key]) {
                return NO;
            }
        }
        // 判断是否需要匹配备孕周期
        if (config.prepare_preg_stage.count > 0 && ![config.prepare_preg_stage containsObject:@(preparePregStage)]) {
            return NO;
        }
        // 服务端没配置记录子项，则不用匹配，直接返回该配置项
        if (!config.sub_items.count) {
            return YES;
        }
        // 判断对应子项是否匹配
        return [config.sub_items bk_any:^BOOL(NSNumber *obj) {
            return obj.integerValue == subItem;
        }];
    }];
    
#ifdef DEBUG
    // 上报给测试验证
    NSMutableDictionary *params = [NSMutableDictionary dictionary];
    params[@"request"] = @{
        @"itemId" : @(itemId),
        @"pageIn" : @(pageIn),
        @"preparePregStage" : @(preparePregStage),
        @"subItem" : @(subItem),
    };
    params[@"return"] = [hitConfig yy_modelToJSONObject];
    [[IMYPublicServerRequest postPath:@"qa-test/analysis_sdk.do" host:@"http://qa-test.seeyouyima.com" params:params headers:nil] subscribeCompleted:^{
        //...
    }];
#endif
    
    // 返回UI素材配置
    return [hitConfig getUIConfig];
}

@end

#pragma mark - 对外暴露的配置属性

@interface IMYSGAnalysisBannerUIConfig ()
@property (nonatomic, assign) NSInteger item_id;
@property (nonatomic, assign) NSInteger vip_material_id;
@property (nonatomic, copy) NSString *text;
@property (nonatomic, copy) NSString *uri;
@property (nonatomic, assign) NSInteger text_index;
@end

@implementation IMYSGAnalysisBannerConfigRemote

+ (NSDictionary<NSString *, id> *)modelContainerPropertyGenericClass {
    return @{
        @"ranges" : IMYSGAnalysisBannerConfigRange.class,
    };
}

- (IMYSGAnalysisBannerUIConfig *)getUIConfig {
    IMYSGAnalysisBannerUIConfig *uiConfig = [IMYSGAnalysisBannerUIConfig new];
    uiConfig.item_id = self.item;
    uiConfig.vip_material_id = self.vip_material_id;
    uiConfig.uri = self.uri;
    // 获取显示的文本，内部会进行轮播
    NSArray * const text_material = self.text_material;
    if (text_material.count > 0) {
        NSString * const kvKey = [NSString stringWithFormat:@"vip_analysis_banner_%ld_%ld", _item, _vip_material_id];
        NSInteger textIndex = [[IMYKV defaultKV] integerForKey:kvKey];
        if (textIndex >= text_material.count) {
            textIndex = 0;
        }
        uiConfig.text = text_material[textIndex];
        uiConfig.text_index = textIndex;
        
        // 存储下次显示的索引
        [[IMYKV defaultKV] setInteger:textIndex + 1 forKey:kvKey];
    }
    return uiConfig;
}

@end

@implementation IMYSGAnalysisBannerConfigRange

@end

@implementation IMYSGAnalysisBannerUIConfig

@end

@implementation IMYSGAnalysisAPIResults

+ (instancetype)resultWithItem:(NSInteger)itemId timeRange:(NSInteger)timeRange resultId:(NSInteger)resultId {
    IMYSGAnalysisAPIResults *result = [IMYSGAnalysisAPIResults new];
    result.item_id = itemId;
    result.time_range = timeRange;
    result.result_id = resultId;
    return result;
}

@end
