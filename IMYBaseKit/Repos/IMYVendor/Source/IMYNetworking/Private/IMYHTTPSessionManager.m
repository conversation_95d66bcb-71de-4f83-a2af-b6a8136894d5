//
//  IMYHTTPSessionManager.m
//  IMYVendor
//
//  Created by mario on 2017/9/8.
//  Copyright © 2017年 meiyou. All rights reserved.
//

#import "IMYHTTPSessionManager.h"
#import "IMYFoundation.h"

//
//NOTE: Make some private methods public
//

@interface AFURLSessionManagerTaskDelegate : NSObject

@property (nonatomic, weak) id manager;

@property (nonatomic, copy) void (^receiveDataBlock)(NSData *data, BOOL finished);

@end

@interface AFURLSessionManager (IMYHTTPSessionManager)

- (void)removeDelegateForTask:(NSURLSessionTask *)task;

- (AFURLSessionManagerTaskDelegate *)delegateForTask:(NSURLSessionTask *)task;

@property (nonatomic, readonly) id sessionDidReceiveAuthenticationChallenge;

@end

@interface IMYHTTPSessionManager ()

@property (nonatomic, weak) NSURLSessionTask *sessionTask;
@property (nonatomic, weak) AFHTTPSessionManager *sessionManager;

@end

@implementation IMYHTTPSessionManager

- (instancetype)initWithBaseURL:(NSURL *)baseURL {
    self = [super init];
    if (self) {
        self.baseURL = baseURL;
    }
    return self;
}

- (void)setupInlineSessionManager:(AFHTTPSessionManager *)sessionManager {
    if (sessionManager) {
        self.sessionManager = sessionManager;
    }
}

- (instancetype)init {
    @throw [NSException exceptionWithName:NSInternalInconsistencyException
                                   reason:[NSString stringWithFormat:@"Unexpected deadly init invokation '%@', use %@ instead.",
                                                                     NSStringFromSelector(_cmd),
                                                                     NSStringFromSelector(@selector(initWithBaseURL:))]
                                 userInfo:nil];
}

///除非手动重置，否则该host的 securityPolicy 不进行释放
+ (NSMapTable *)shareSecurityPolicyMap {
    static NSMapTable *mapTable;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        mapTable = [NSMapTable strongToStrongObjectsMapTable];
    });
    return mapTable;
}

/// 友盟开启网络Hook后，需要重新创建一个 AFHTTPSessionManager
static BOOL kUsingInternalSessionManager = NO;
+ (void)resetInternalSessionManager {
    if (kUsingInternalSessionManager) {
        return;
    }
    // 先创建对应AFSession
    [IMYHTTPSessionManager sharedInternalAFSessionManager];
    // 在打开标记
    kUsingInternalSessionManager = YES;
}

+ (AFHTTPSessionManager *)createAFSessionManager {
    NSURLSessionConfiguration *configuration = nil;
    if ([IMYHTTPSessionManager respondsToSelector:@selector(customizingSessionConfiguration)]) {
        configuration = [IMYHTTPSessionManager customizingSessionConfiguration];
    }
    AFHTTPSessionManager *instance = [[AFHTTPSessionManager alloc] initWithBaseURL:nil sessionConfiguration:configuration];
    [instance setSessionDidReceiveAuthenticationChallengeBlock:^NSURLSessionAuthChallengeDisposition(NSURLSession *_Nonnull session, NSURLAuthenticationChallenge *_Nonnull challenge, NSURLCredential *__autoreleasing _Nullable *_Nullable credential) {
        return [IMYHTTPSessionManager URLSession:session didReceiveChallenge:challenge returnCredential:credential];
    }];
    return instance;
}

+ (AFHTTPSessionManager *)sharedAFSessionManager {
    static AFHTTPSessionManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [IMYHTTPSessionManager createAFSessionManager];
    });
    return instance;
}

+ (AFHTTPSessionManager *)sharedInternalAFSessionManager {
    static AFHTTPSessionManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [IMYHTTPSessionManager createAFSessionManager];
    });
    return instance;
}

+ (BOOL)evaluateServerTrust:(SecTrustRef)serverTrust forDomain:(NSString *)domain {
    NSMutableArray *policies = [NSMutableArray array];
    if (domain) {
        [policies addObject:(__bridge_transfer id)SecPolicyCreateSSL(true, (__bridge CFStringRef)domain)];
    } else {
        [policies addObject:(__bridge_transfer id)SecPolicyCreateBasicX509()];
    }

    SecTrustSetPolicies(serverTrust, (__bridge CFArrayRef)policies);

    SecTrustResultType result;
    SecTrustEvaluate(serverTrust, &result);

    return (result == kSecTrustResultUnspecified || result == kSecTrustResultProceed);
}

+ (NSURLSessionAuthChallengeDisposition)URLSession:(NSURLSession *)session
                               didReceiveChallenge:(NSURLAuthenticationChallenge *)challenge
                                  returnCredential:(NSURLCredential **)credential {
    NSURLSessionAuthChallengeDisposition disposition = NSURLSessionAuthChallengePerformDefaultHandling;
    if ([challenge.protectionSpace.authenticationMethod isEqualToString:NSURLAuthenticationMethodServerTrust]) {

        NSString *host = challenge.protectionSpace.host;
        SecTrustRef serverTrust = challenge.protectionSpace.serverTrust;

        // security policy
        AFSecurityPolicy *securityPolicy = [[IMYHTTPSessionManager shareSecurityPolicyMap] objectForKey:host];
        if (securityPolicy) {
            if ([securityPolicy evaluateServerTrust:serverTrust forDomain:host]) {
                *credential = [NSURLCredential credentialForTrust:serverTrust];
                if (*credential) {
                    disposition = NSURLSessionAuthChallengeUseCredential;
                }
            } else {
                disposition = NSURLSessionAuthChallengeCancelAuthenticationChallenge;
            }
        } else {
            // https dns
            NSString *domain = [IMYHTTPSessionManager httpsDomainForIp:host];
            if (domain) {
                if ([IMYHTTPSessionManager evaluateServerTrust:serverTrust forDomain:domain]) {
                    *credential = [NSURLCredential credentialForTrust:serverTrust];
                    if (*credential) {
                        disposition = NSURLSessionAuthChallengeUseCredential;
                    }
                } else {
                    disposition = NSURLSessionAuthChallengeCancelAuthenticationChallenge;
                }
            }
        }
    }
    return disposition;
}

- (NSURLSessionDataTask *)GET:(NSString *)URLString
                   parameters:(id)parameters
                      success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                      failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    return [self GET:URLString parameters:parameters progress:nil success:success failure:failure];
}

- (NSURLSessionDataTask *)GET:(NSString *)URLString
                   parameters:(id)parameters
                     progress:(void (^)(NSProgress *_Nonnull))downloadProgress
                      success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                      failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSURLSessionDataTask *dataTask = [self dataTaskWithHTTPMethod:@"GET"
                                                        URLString:URLString
                                                       parameters:parameters
                                                   uploadProgress:nil
                                                 downloadProgress:downloadProgress
                                                          success:success
                                                          failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)HEAD:(NSString *)URLString
                    parameters:(id)parameters
                       success:(void (^)(NSURLSessionDataTask *_Nonnull))success
                       failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSURLSessionDataTask *dataTask = [self dataTaskWithHTTPMethod:@"HEAD"
                                                        URLString:URLString
                                                       parameters:parameters
                                                   uploadProgress:nil
                                                 downloadProgress:nil
                                                          success:^(NSURLSessionDataTask *task, __unused id responseObject) {
                                                              if (success) {
                                                                  success(task);
                                                              }
                                                          }
                                                          failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(id)parameters
                       success:(void (^)(NSURLSessionDataTask *task, id responseObject))success
                       failure:(void (^)(NSURLSessionDataTask *task, NSError *error))failure {
    return [self POST:URLString parameters:parameters progress:nil success:success failure:failure];
}

- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(id)parameters
                      progress:(void (^)(NSProgress *_Nonnull))uploadProgress
                       success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                       failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSURLSessionDataTask *dataTask = [self dataTaskWithHTTPMethod:@"POST"
                                                        URLString:URLString
                                                       parameters:parameters
                                                   uploadProgress:uploadProgress
                                                 downloadProgress:nil
                                                          success:success
                                                          failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(id)parameters
     constructingBodyWithBlock:(void (^)(id<AFMultipartFormData> _Nonnull))block
                       success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                       failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    return [self POST:URLString parameters:parameters constructingBodyWithBlock:block progress:nil success:success failure:failure];
}

- (NSURLSessionDataTask *)POST:(NSString *)URLString
                    parameters:(id)parameters
     constructingBodyWithBlock:(void (^)(id<AFMultipartFormData> _Nonnull))block
                      progress:(void (^)(NSProgress *_Nonnull))uploadProgress
                       success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                       failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSError *serializationError = nil;
    NSMutableURLRequest *request = [self.requestSerializer multipartFormRequestWithMethod:@"POST"
                                                                                URLString:[[NSURL URLWithString:URLString relativeToURL:self.baseURL] absoluteString]
                                                                               parameters:parameters
                                                                constructingBodyWithBlock:block
                                                                                    error:&serializationError];
    request.cachePolicy = self.requestCachePolicy;

    if (serializationError) {
        if (failure) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgnu"
            dispatch_async(self.completionQueue ?: dispatch_get_main_queue(), ^{
                failure(nil, serializationError);
            });
#pragma clang diagnostic pop
        }
        return nil;
    }

    NSURLSessionDataTask *uploadTask = [self uploadTaskWithStreamedRequest:request
                                                            uploadProgress:uploadProgress
                                                          downloadProgress:nil
                                                                   success:success
                                                                   failure:failure];
    return uploadTask;
}

- (NSURLSessionDataTask *)PUT:(NSString *)URLString
                   parameters:(id)parameters
                      success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                      failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSURLSessionDataTask *dataTask = [self dataTaskWithHTTPMethod:@"PUT"
                                                        URLString:URLString
                                                       parameters:parameters
                                                   uploadProgress:nil
                                                 downloadProgress:nil
                                                          success:success
                                                          failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)PATCH:(NSString *)URLString
                     parameters:(id)parameters
                        success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                        failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSURLSessionDataTask *dataTask = [self dataTaskWithHTTPMethod:@"PATCH"
                                                        URLString:URLString
                                                       parameters:parameters
                                                   uploadProgress:nil
                                                 downloadProgress:nil
                                                          success:success
                                                          failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)DELETE:(NSString *)URLString
                      parameters:(id)parameters
                         success:(void (^)(NSURLSessionDataTask *, id))success
                         failure:(void (^)(NSURLSessionDataTask *, NSError *))failure {
    NSURLSessionDataTask *dataTask = [self dataTaskWithHTTPMethod:@"DELETE"
                                                        URLString:URLString
                                                       parameters:parameters
                                                   uploadProgress:nil
                                                 downloadProgress:nil
                                                          success:success
                                                          failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)dataTaskWithHTTPMethod:(NSString *)method
                                       URLString:(NSString *)URLString
                                      parameters:(id)parameters
                                  uploadProgress:(void (^)(NSProgress *))uploadProgress
                                downloadProgress:(void (^)(NSProgress *))downloadProgress
                                         success:(void (^)(NSURLSessionDataTask *, id))success
                                         failure:(void (^)(NSURLSessionDataTask *, NSError *))failure {
    NSError *serializationError = nil;
    NSMutableURLRequest *request = [self.requestSerializer requestWithMethod:method
                                                                   URLString:[[NSURL URLWithString:URLString relativeToURL:self.baseURL] absoluteString]
                                                                  parameters:parameters
                                                                       error:&serializationError];
    request.cachePolicy = self.requestCachePolicy;

    if (serializationError) {
        if (failure) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgnu"
            dispatch_async(self.completionQueue ?: dispatch_get_main_queue(), ^{
                failure(nil, serializationError);
            });
#pragma clang diagnostic pop
        }
        return nil;
    }
    NSURLSessionDataTask *dataTask = [self dataTaskWithRequest:request
                                                uploadProgress:uploadProgress
                                              downloadProgress:downloadProgress
                                                       success:success
                                                       failure:failure];
    return dataTask;
}

- (NSURLSessionDataTask *)dataTaskWithRequest:(NSURLRequest *)request
                               uploadProgress:(void (^)(NSProgress *_Nonnull))uploadProgress
                             downloadProgress:(void (^)(NSProgress *_Nonnull))downloadProgress
                                      success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                                      failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSMutableURLRequest *mutableRequest = [request imy_mutableConverted];

    if (self.willRequestHook) {
        self.willRequestHook(mutableRequest);
    }
    
    [self gzipEncodingActionByRequest:mutableRequest];

    __block NSURLSessionDataTask *dataTask = nil;
    dataTask = [self.sessionManager dataTaskWithRequest:mutableRequest
                                         uploadProgress:uploadProgress
                                       downloadProgress:downloadProgress
                                      completionHandler:^(NSURLResponse *_Nonnull response, id _Nullable responseObject, NSError *_Nullable error) {
                                          [self completeHandler:dataTask response:response responseObject:responseObject error:error success:success failure:failure];
                                      }];

    [self injectDelegateWithTask:dataTask];

    [dataTask resume];

    return dataTask;
}

- (NSURLSessionUploadTask *)uploadTaskWithStreamedRequest:(NSURLRequest *)request
                                           uploadProgress:(void (^)(NSProgress *_Nonnull))uploadProgress
                                         downloadProgress:(void (^)(NSProgress *_Nonnull))downloadProgress
                                                  success:(void (^)(NSURLSessionDataTask *_Nonnull, id _Nullable))success
                                                  failure:(void (^)(NSURLSessionDataTask *_Nullable, NSError *_Nonnull))failure {
    NSMutableURLRequest *mutableRequest = [request imy_mutableConverted];

    if (self.willRequestHook) {
        self.willRequestHook(mutableRequest);
    }
    
    [self gzipEncodingActionByRequest:mutableRequest];

    __block NSURLSessionUploadTask *uploadTask = nil;
    uploadTask = [self.sessionManager uploadTaskWithStreamedRequest:mutableRequest
                                                           progress:uploadProgress
                                                  completionHandler:^(NSURLResponse *_Nonnull response, id _Nullable responseObject, NSError *_Nullable error) {
                                                      [self completeHandler:uploadTask response:response responseObject:responseObject error:error success:success failure:failure];
                                                  }];

    [self injectDelegateWithTask:uploadTask];

    [uploadTask resume];

    return uploadTask;
}

- (void)completeHandler:(NSURLSessionDataTask *)dataTask
               response:(NSURLResponse *)response
         responseObject:(id)responseObject
                  error:(NSError *)error
                success:(void (^)(NSURLSessionDataTask *, id))success
                failure:(void (^)(NSURLSessionDataTask *, NSError *))failure {
    if (error) {
        if (failure) {
            failure(dataTask, error);
        }
    } else {
        if (success) {
            success(dataTask, responseObject);
        }
    }
}

- (void)gzipEncodingActionByRequest:(NSMutableURLRequest *)request {
    ///判断是否有gzip压缩
    if (!self.gzipEncoding) {
        return;
    }
    NSData *ungzipData = nil;
    if (request.HTTPBodyStream) {
        NSMutableData *mutableData = [NSMutableData data];
        NSInputStream *inputStream = request.HTTPBodyStream;
        [inputStream open];
        uint8_t buffer[10240];
        NSInteger readLength = 0;
        do {
            readLength = [inputStream read:(uint8_t *)&buffer maxLength:10240];
            if (readLength > 0) {
                [mutableData appendBytes:&buffer length:readLength];
            }
        } while (readLength > 0);
        [inputStream close];
        ungzipData = mutableData;
    } else if (request.HTTPBody.length > 0) {
        ungzipData = request.HTTPBody;
    }
    // 小于256字节的数据，就不进行gzip压缩了
    if (ungzipData.length < 256) {
        return;
    }

    NSData *gzipData = [NSData imy_gzipData:ungzipData];
    // gzip 压缩失败
    if (!gzipData.length || [ungzipData isEqual:gzipData]) {
        return;
    }
    ///gzip 压缩比原始数据 还大
    if (gzipData.length >= ungzipData.length) {
        return;
    }

    ///gzip 数据替换
    request.HTTPBodyStream = nil;
    request.HTTPBody = gzipData;

    [request setValue:[NSString stringWithFormat:@"%ld", (long)gzipData.length] forHTTPHeaderField:@"Content-Length"];
    [request setValue:@"gzip" forHTTPHeaderField:@"Content-Encoding"];
}

- (void)injectDelegateWithTask:(NSURLSessionTask *)task {
    AFURLSessionManagerTaskDelegate *delegate = [self.sessionManager delegateForTask:task];
    delegate.manager = self;
    self.sessionTask = task;
    if (self.securityPolicy) {
        NSString *host = task.originalRequest.URL.host.lowercaseString;
        [[IMYHTTPSessionManager shareSecurityPolicyMap] setObject:self.securityPolicy forKey:host];
    }
    if (self.receiveDataBlock) {
        delegate.receiveDataBlock = self.receiveDataBlock;
    }
}

- (void)cancel {
    if (self.sessionTask) {
        [self.sessionManager removeDelegateForTask:self.sessionTask];
    }
}

- (NSMethodSignature *)methodSignatureForSelector:(SEL)sel {
    return [super methodSignatureForSelector:sel];
}

- (void)forwardInvocation:(NSInvocation *)invocation {
    NSAssert(NO, @"unfindable selector:%@", NSStringFromSelector(invocation.selector));
    void *null = NULL;
    [invocation setReturnValue:&null];
}

@end


/// Hooks

@implementation IMYHTTPSessionManager (Hooks)

/// 初始化已经完成，各个属性已经被设置（baseURL, securityPolicy etc.）
- (void)finishSetupHook {
    if (!self.sessionManager) {
        if (kUsingInternalSessionManager) {
            self.sessionManager = [IMYHTTPSessionManager sharedInternalAFSessionManager];
        } else {
            self.sessionManager = [IMYHTTPSessionManager sharedAFSessionManager];
        }
    }
    if (!self.sessionManager.sessionDidReceiveAuthenticationChallenge) {
        [self.sessionManager setSessionDidReceiveAuthenticationChallengeBlock:^NSURLSessionAuthChallengeDisposition(NSURLSession *_Nonnull session, NSURLAuthenticationChallenge *_Nonnull challenge, NSURLCredential *__autoreleasing _Nullable *_Nullable credential) {
            return [IMYHTTPSessionManager URLSession:session didReceiveChallenge:challenge returnCredential:credential];
        }];
    }
}

static id<IMYHTTPSDNSProtocol> IMYHTTPSDNS = nil;
+ (void)setHTTPSDNS:(id<IMYHTTPSDNSProtocol>)httpsdns {
    IMYHTTPSDNS = httpsdns;
}

+ (NSString *)httpsDomainForIp:(NSString *)ip {
    return [IMYHTTPSDNS httpsDomainForIp:ip];
}

@end
