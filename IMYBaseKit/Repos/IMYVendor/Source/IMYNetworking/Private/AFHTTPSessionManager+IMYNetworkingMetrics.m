//
//  AFHTTPSessionManager+IMYNetworkingMetrics.m
//  IMYNetworking
//

#import "AFHTTPSessionManager+IMYNetworkingMetrics.h"
#import "IMYNetworkingTaskMetrics.h"
#import "IMYConfigsCenter.h"

@implementation AFHTTPSessionManager (IMYNetworkingMetrics)

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task didFinishCollectingMetrics:(NSURLSessionTaskMetrics *)metrics {
    // 根据配置开关决定是否启用网络性能数据收集
    BOOL enableNetworkMetrics = [[IMYConfigsCenter sharedInstance] boolForKeyPath:@"apptech.errors.meiyou_net_hook"];
    
    if (!enableNetworkMetrics) {
        return;
    }
    IMYNetworkingTaskMetrics *taskMetrics = [[IMYNetworkingTaskMetrics alloc] initWithMetrics:metrics];
    [[IMYNetworkingTaskMetricsManager sharedManager] setTaskMetrics:taskMetrics forTask:task];
}

@end
