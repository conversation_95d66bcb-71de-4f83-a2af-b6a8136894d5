//
//  IMYNetworkingTaskMetrics.h
//  IMYNetworking
//

#import <Foundation/Foundation.h>
#import "IMYHTTPInternal.h"

NS_ASSUME_NONNULL_BEGIN

API_AVAILABLE(ios(10.0))
@interface IMYNetworkingTaskMetrics : NSObject <IMYNetworkingTaskMetricsProtocol>

/**
*  根据指标初始化类
*
*  @param metrics 指标。
*  @return 返回实例不是全部的指标都有数据，目前仅收集耗时相关的数据指标。
*/
- (instancetype)initWithMetrics:(NSURLSessionTaskMetrics *)metrics;

@end

/**
 * 网络任务性能指标管理器
 * 用于存储和获取NSURLSessionTask对应的性能数据
 */
@interface IMYNetworkingTaskMetricsManager : NSObject

+ (instancetype)sharedManager;

/**
 * 存储task对应的性能指标
 * @param metrics 性能指标对象
 * @param task 网络任务
 */
- (void)setTaskMetrics:(IMYNetworkingTaskMetrics *)metrics forTask:(NSURLSessionTask *)task;

/**
 * 获取task对应的性能指标
 * @param task 网络任务
 * @return 性能指标对象，如果不存在返回nil
 */
- (nullable IMYNetworkingTaskMetrics *)taskMetricsForTask:(NSURLSessionTask *)task;

/**
 * 移除task对应的性能指标（需要主动清理）
 * @param task 网络任务
 */
- (void)removeTaskMetricsForTask:(NSURLSessionTask *)task;

@end

NS_ASSUME_NONNULL_END
