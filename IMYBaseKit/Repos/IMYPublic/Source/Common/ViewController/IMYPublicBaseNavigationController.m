//
//  IMYVKBaseNavigationController.m
//  IMY_ViewKit
//
//  Created by ljh on 15/5/20.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYPublicBaseNavigationController.h"
#import "IMYPublic.h"

NSString *const kIMYNavigationWillPushNotification = @"kIMYNavigationWillPushNotification";
NSString *const kIMYNavigationDidPopNotification = @"kIMYNavigationDidPopNotification";

@interface IMYFullScreenPanGestureRecognizer : UIPanGestureRecognizer
@property (nonatomic, weak) id currentPanTarget;
@end

@interface IMYPublicBaseNavigationController ()
@property (nonatomic, assign) BOOL isViewWillAppeared; /**< 页面是否即将显示*/
@property (nonatomic, assign) BOOL isViewDidAppeared;  /**< 页面是否已经显示过*/
@property (nonatomic, assign) BOOL isViewWillDisappear;  /**< 页面即将不显示*/
@property (nonatomic, assign) BOOL isViewActived;      /**< 页面是否处于激活状态*/
@property (nonatomic, assign) BOOL isTopItemsFixing;   /**< 是否正在修正top bar items*/

@end

@interface IMYPublicBaseNavigationController ()
@property (nonatomic, strong) IMYFullScreenPanGestureRecognizer *fullPanGestureRecognizer;
@property (nonatomic, assign) BOOL enableFullPanGestureRecognizer;
@property (nonatomic, assign) BOOL respondsFullPanEnableSEL;
@property (nonatomic, assign) double panReceiveTouchWidth;
@property (nonatomic, assign) double panBeginTouchWidth;

@end

@implementation IMYPublicBaseNavigationController {
    BOOL isCalledInitMyself;
}
@dynamic delegate;

- (instancetype)initWithCoder:(NSCoder *)coder {
    self = [super initWithCoder:coder];
    if (self) {
        [self callInitMyself];
    }
    return self;
}

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil {
    self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil];
    if (self) {
        [self callInitMyself];
    }
    return self;
}

- (instancetype)initWithRootViewController:(UIViewController *)rootViewController {
    self = [super initWithRootViewController:rootViewController];
    if (self) {
        _wrapRootVC = rootViewController;
        [self callInitMyself];
    }
    return self;
}

- (instancetype)initWithNavigationBarClass:(Class)navigationBarClass toolbarClass:(Class)toolbarClass {
    self = [super initWithNavigationBarClass:navigationBarClass toolbarClass:toolbarClass];
    if (self) {
        [self callInitMyself];
    }
    return self;
}

- (void)callInitMyself {
    if (!isCalledInitMyself) {
        isCalledInitMyself = YES;
        [self _initMyself];
    }
}

- (void)_initMyself {
    _usingRootVCTabBarItem = YES;
    _canAction = YES;
    _panReceiveTouchWidth = SCREEN_WIDTH;
    _panBeginTouchWidth = SCREEN_WIDTH;
    if (@available(iOS 13.0, *)) {
        // 在 iOS13 上默认为 UIModalPresentationAutomatic，需要改为全屏
        self.modalPresentationStyle = UIModalPresentationFullScreen;
        // 在 iOS13 上强制为 UIUserInterfaceStyleLight (浅色，不跟随系统换肤)
        self.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
    }
    [super setDelegate:self];
}

- (void)viewDidLoad {
    // 属性初始化
    [self callInitMyself];
    // 系统初始化
    [super viewDidLoad];
    // RightToLeft?
    if (IMYLanguageManager.supportMultiLanguages) {
        [self imy_addLanguageLayoutsChangeActionBlock:^(UIViewController *weakObject) {
            weakObject.view.semanticContentAttribute = IMYLanguageManager.sharedInstance.semanticContentAttribute;
        }];
    }
    
    // 初始化全屏返回手势
    [self setupFullscreenPopGesture];
    
    // 监听App前后台变化, 修改 isViewActived
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onBaseApplicationDidBecomeActive) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(onBaseApplicationDidEnterBackground) name:UIApplicationDidEnterBackgroundNotification object:nil];
    
    // 设置为 YES 让外部可以通过KVO 来获取消息
    [self willChangeValueForKey:@"isViewLoaded"];
    [self didChangeValueForKey:@"isViewLoaded"];
    
    // 发送 ViewDidLoad 通知
    NSString *viewLoadedName = IMYPublicBaseViewController.IMYViewControllerViewDidLoadNotification;
    [[NSNotificationCenter defaultCenter] postNotificationName:viewLoadedName
                                                        object:self];
}

- (void)onBaseApplicationDidBecomeActive {
    NSString *queueKey = [NSString stringWithFormat:@"VCDidBecomeActiveDelay_%p", self];
    @weakify(self);
    [NSObject imy_asyncBlock:^{
        @strongify(self);
        if (!self.isViewWillDisappear && self.isViewDidAppeared && self.isViewLoaded && self.view.window &&
            IMYGetApplicationState() == UIApplicationStateActive) {
            self.isViewActived = YES;
        }
    } onLevel:IMYQueueLevelMain afterSecond:1 forKey:queueKey];
}

- (void)onBaseApplicationDidEnterBackground {
    NSString *queueKey = [NSString stringWithFormat:@"VCDidBecomeActiveDelay_%p", self];
    [NSObject imy_cancelBlockForKey:queueKey];
    if (self.isViewActived) {
        self.isViewActived = NO;
    }
}

- (void)setIsViewActived:(BOOL const)isViewActived {
    if (isViewActived == _isViewActived) {
        return;
    }
    _isViewActived = isViewActived;
    // 判断是否在释放中
    if (imy_weak_check_deallocating(self)) {
        return;
    }
    // 发送变化通知
    NSString *viewActivedName = IMYPublicBaseViewController.IMYViewControllerDidActiveChangedNotification;
    [[NSNotificationCenter defaultCenter] postNotificationName:viewActivedName
                                                        object:self];
}

- (void)setupFullscreenPopGesture {
    UIPanGestureRecognizer *interactivePopGestureRecognizer = (id)[super interactivePopGestureRecognizer];
    /// 增加我们自己的控制条件
    interactivePopGestureRecognizer.delegate = self;
    
    // 获取系统回调
    IMYKVCHashMap *hashMap = interactivePopGestureRecognizer.imykvc_hashMap;
    if (hashMap.target && hashMap.action) {
        IMYFullScreenPanGestureRecognizer *panGestureRecognizer = [[IMYFullScreenPanGestureRecognizer alloc] initWithTarget:hashMap.target
                                                                                                                     action:hashMap.action];
        panGestureRecognizer.maximumNumberOfTouches = 1;
        panGestureRecognizer.delegate = self;
        panGestureRecognizer.cancelsTouchesInView = YES;
        panGestureRecognizer.currentPanTarget = hashMap.target;
        [self.view addGestureRecognizer:panGestureRecognizer];
        self.fullPanGestureRecognizer = panGestureRecognizer;
        /// 取消原有的全屏返回手势，不使用原有的全屏返回手势
        interactivePopGestureRecognizer.enabled = NO;
    } else {
        self.fullPanGestureRecognizer = (id)interactivePopGestureRecognizer;
    }
    self.enableFullPanGestureRecognizer = YES;
}

- (void)setEnableFullPanGestureRecognizer:(BOOL)enableFullPanGestureRecognizer {
    _enableFullPanGestureRecognizer = enableFullPanGestureRecognizer;
    [self setCurrentPageEnableFullPanGestureRecognizer:enableFullPanGestureRecognizer];
}

- (void)setCurrentPageEnableFullPanGestureRecognizer:(BOOL)enable {
    if (enable && self.enableFullPanGestureRecognizer) {
        self.panBeginTouchWidth = SCREEN_WIDTH;
    } else {
        self.panBeginTouchWidth = self.defaultFullPanLeftMaxBeginWidth;
    }
}

- (UIGestureRecognizer *)interactivePopGestureRecognizer {
    return self.fullPanGestureRecognizer;
}

#pragma mark - vc lifecycle

- (BOOL)isViewLoaded {
    return [super isViewLoaded];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.isViewWillAppeared = YES;
    self.isViewWillDisappear = NO;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.isViewActived = NO;
    self.isViewWillDisappear = YES;
}

- (void)viewDidDisappear:(BOOL)animated {
    [super viewDidDisappear:animated];
    self.isViewActived = NO;
    self.isViewWillDisappear = YES;
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];
    self.isViewActived = YES;
    self.isViewDidAppeared = YES;
    self.isViewWillDisappear = NO;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - can action check
- (NSString *)actionDelayResetQueueKey {
    return [NSString stringWithFormat:@"IMYNavActionDelayResetMark_%p", self];
}

- (void)actionDelayResetMark {
    // 已有重置操作
    if ([NSObject imy_hasAsyncBlockForKey:self.actionDelayResetQueueKey]) {
        return;
    }
    // 一秒后重置
    [NSObject imy_asyncBlock:^{
        [self resetActionMark];
    } onQueue:dispatch_get_main_queue() afterSecond:1 forKey:self.actionDelayResetQueueKey];
}

- (void)resetActionMark {
    self.canAction = YES;
}

- (BOOL)checkWillPopActionWithAnimated:(BOOL *)animated {
    const UIGestureRecognizerState state = self.interactivePopGestureRecognizer.state;
    const BOOL isGestureAction = (state == UIGestureRecognizerStateBegan ||
                                  state == UIGestureRecognizerStateChanged);
    if (!isGestureAction) {
        if (!self.canAction) {
            [self actionDelayResetMark];
            return YES;
        }
        if (*animated) {
            self.canAction = NO;
            [self actionDelayResetMark];

            if (self.isViewLoaded && !self.view.window) {
                *animated = NO;
            }
        }
    }
    return NO;
}

#pragma mark - pop & push

- (NSString *)popQueueKey {
    return [NSString stringWithFormat:@"popQueueKey_%p", self];
}

- (NSArray<UIViewController *> *)popToViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if (![NSThread isMainThread]) {
        NSAssert(NO, @"不在主线程，调用了UI方法");
        return @[];
    }
    if (!viewController) {
        NSAssert(NO, @"viewController is nil!");
        return @[];
    }
    if ([self checkWillPopActionWithAnimated:&animated]) {
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            // 修复太早调用pop无法生效问题
            [self popToViewController:viewController animated:animated];
        } onQueue:dispatch_get_main_queue() afterSecond:0.3 forKey:self.popQueueKey];
        return @[];
    }
    [NSObject imy_cancelBlockForKey:self.popQueueKey];
    
    // fix iOS开发 -- iOS 14下popToRootViewControllerAnimated:YES 导致TabBar隐藏的问题
    if (@available(iOS 14, *)) {
        if (self.viewControllers.firstObject == viewController) {
            self.viewControllers.firstObject.hidesBottomBarWhenPushed = NO;
            self.viewControllers.lastObject.hidesBottomBarWhenPushed = NO;
        }
    }
    // pop
    NSArray<UIViewController *> *array = [super popToViewController:viewController animated:animated];
    
    // 发送 did pop 通知
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMYNavigationDidPopNotification object:array];
    
    return array;
}

- (NSArray<UIViewController *> *)popToRootViewControllerAnimated:(BOOL)animated {
    if (![NSThread isMainThread]) {
        NSAssert(NO, @"不在主线程，调用了UI方法");
        return @[];
    }
    if ([self checkWillPopActionWithAnimated:&animated]) {
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            // 修复太早调用pop无法生效问题
            [self popToRootViewControllerAnimated:animated];
        } onQueue:dispatch_get_main_queue() afterSecond:0.3 forKey:self.popQueueKey];
        return @[];
    }
    [NSObject imy_cancelBlockForKey:self.popQueueKey];
    
    // fix iOS开发 -- iOS 14下popToRootViewControllerAnimated:YES 导致TabBar隐藏的问题
    if (@available(iOS 14, *)) {
        self.viewControllers.firstObject.hidesBottomBarWhenPushed = NO;
        self.viewControllers.lastObject.hidesBottomBarWhenPushed = NO;
    }
    // pop
    NSArray<UIViewController *> *array = [super popToRootViewControllerAnimated:animated];
    
    // 发送 did pop 通知
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMYNavigationDidPopNotification object:array];
    
    return array;
}

- (UIViewController *)popViewControllerAnimated:(BOOL)animated {
    if (![NSThread isMainThread]) {
        NSAssert(NO, @"不在主线程，调用了UI方法");
        return nil;
    }
    // 电商逻辑，修正百川VC没带动画问题
    if ([NSStringFromClass(self.viewControllers.lastObject.class) hasPrefix:@"Tae"]) {
        animated = YES;
    }
    
    // 手势动画中，0.3秒后再执行pop
    if ([self checkWillPopActionWithAnimated:&animated]) {
        @weakify(self);
        [NSObject imy_asyncBlock:^{
            @strongify(self);
            // 修复太早调用pop无法生效问题
            [self popViewControllerAnimated:animated];
        } onQueue:dispatch_get_main_queue() afterSecond:0.3 forKey:self.popQueueKey];
        return nil;
    }
    [NSObject imy_cancelBlockForKey:self.popQueueKey];
    
    // fix iOS开发 -- iOS 14下popToRootViewControllerAnimated:YES 导致TabBar隐藏的问题
    if (@available(iOS 14, *) && self.viewControllers.count == 2) {
        self.viewControllers.firstObject.hidesBottomBarWhenPushed = NO;
        self.viewControllers.lastObject.hidesBottomBarWhenPushed = NO;
    }
    
    // pop
    UIViewController *viewController = [super popViewControllerAnimated:animated];
    
    // 发送 did pop 通知
    if (viewController) {
        [[NSNotificationCenter defaultCenter] postNotificationName:kIMYNavigationDidPopNotification object:@[viewController]];
    }
    
    return viewController;
}

- (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated {
    if (![NSThread isMainThread]) {
        NSAssert(NO, @"不在主线程，调用了UI方法");
        return;
    }

    if (!viewController) {
        NSAssert(NO, @"viewController is nil!");
        return;
    }
    
    if (!_wrapRootVC) {
        _wrapRootVC = viewController;
    }
    // 修正未调用 initMyself 的情况
    if (!isCalledInitMyself) {
        [self callInitMyself];
    }
    
    // 如果还在执行 push 操作，则这次push不能执行
    if (!self.canAction) {
        [self actionDelayResetMark];
        if (self.viewControllers.lastObject.class == viewController.class) {
#ifdef DEBUG
            NSString *message = [NSString stringWithFormat:@"短时间内不支持相同页面 多次Push -> %@", viewController.debugDescription];
            [UIAlertView imy_quickAlert:message];
#endif
        } else {
            // 0.3秒后再次尝试
            imy_asyncMainBlock(0.3, ^{
                [self pushViewController:viewController animated:animated];
            });
        }
        return;
    }
    
    // 已在堆栈内
    if ([self.viewControllers containsObject:viewController]) {
        NSAssert(NO, @"%@ 已在push堆栈内，无法push!", viewController);
        return;
    }
    
    // 给外部发送 will push 通知
    [[NSNotificationCenter defaultCenter] postNotificationName:kIMYNavigationWillPushNotification object:viewController];
    
    // 电商逻辑，修正百川VC没带动画问题
    if ([NSStringFromClass(viewController.class) hasPrefix:@"Tae"]) {
        animated = YES;
    }
    
    if (animated) {
        self.canAction = NO;
        [self actionDelayResetMark];
    }
    
    // 修正 hides tabbar 值
    [self resetPageHidesTabBar:viewController atIndex:self.viewControllers.count];
    // 修正历史堆栈的 hides tabbar 值
    [self resetCurrentPagesHidesTabBar];
    
    // nav bar 换肤监听
    [self setViewWillAppearInjectNavBarHidenBlockInVC:viewController];
    
    // 取消之前的pop命令
    [NSObject imy_cancelBlockForKey:self.popQueueKey];
    
    if (self.isViewLoaded && !self.view.window) {
        animated = NO;
    }
    
    // push动画期间，取消返回手势响应
    self.interactivePopGestureRecognizer.enabled = NO;
    
    // 执行Push
    [super pushViewController:viewController animated:animated];

    // 修正 iPhoneX 机型上， 底部 tabBar 高度不对的问题
    if (iPhoneX) {
        CGRect frame = self.tabBarController.tabBar.frame;
        frame.origin.y = SCREEN_HEIGHT - frame.size.height;
        self.tabBarController.tabBar.frame = frame;
    }
}

- (void)setViewControllers:(NSArray<__kindof UIViewController *> *)viewControllers {
    if (![NSThread isMainThread]) {
        NSAssert(NO, @"不在主线程，调用了UI方法");
        return;
    }
    // 无内容，不进行设置
    if (!viewControllers.count) {
        return;
    }
    NSInteger currentIndex = 0;
    for (UIViewController *viewController in viewControllers) {
        // nav bar 换肤监听
        [self setViewWillAppearInjectNavBarHidenBlockInVC:viewController];
        // 修正 hides tabbar 值
        [self resetPageHidesTabBar:viewController atIndex:currentIndex];
        // 递增
        currentIndex += 1;
    }
    // 判断是否需要替换 root VC
    _wrapRootVC = viewControllers.firstObject;
    
    [super setViewControllers:viewControllers];
}

- (void)setViewControllers:(NSArray<UIViewController *> *)viewControllers animated:(BOOL)animated {
    if (![NSThread isMainThread]) {
        NSAssert(NO, @"不在主线程，调用了UI方法");
        return;
    }
    // 无内容，不进行设置
    if (!viewControllers.count) {
        return;
    }
    NSInteger currentIndex = 0;
    for (UIViewController *viewController in viewControllers) {
        // nav bar 换肤监听
        [self setViewWillAppearInjectNavBarHidenBlockInVC:viewController];
        // 修正 hides tabbar 值
        [self resetPageHidesTabBar:viewController atIndex:currentIndex];
        // 递增
        currentIndex += 1;
    }
    // 替换 root VC
    _wrapRootVC = viewControllers.firstObject;
    
    [super setViewControllers:viewControllers animated:animated];
}

- (void)resetCurrentPagesHidesTabBar {
    NSInteger currentIndex = 0;
    for (UIViewController *viewController in self.viewControllers) {
        // 修正 hides tabbar 值
        [self resetPageHidesTabBar:viewController atIndex:currentIndex];
        // 递增
        currentIndex += 1;
    }
}

- (void)resetPageHidesTabBar:(UIViewController *)viewController
                     atIndex:(NSInteger)atIndex {
    // 修正 hides tabbar 值
    const NSInteger forceHides = viewController.imy_config.hidesBottomBarWhenPushed;
    if (forceHides == 1) { // 强制隐藏
        viewController.hidesBottomBarWhenPushed = YES;
    } else if (forceHides == 2) { // 强制不隐藏
        viewController.hidesBottomBarWhenPushed = NO;
    } else { // 智能判断：二级页面以上才隐藏
        if (atIndex > 0) {
            viewController.hidesBottomBarWhenPushed = YES;
        } else {
            viewController.hidesBottomBarWhenPushed = NO;
        }
    }
}

/// nav bar 换肤监听
- (void)setViewWillAppearInjectNavBarHidenBlockInVC:(UIViewController *)viewController {
    __weak typeof(self) wSelf = self;
    void (^viewWillAppearInjectNavBarHidenBlock)(IMYPublicBaseViewController *viewController, BOOL animated) = ^(IMYPublicBaseViewController *viewController, BOOL animated) {
        __strong typeof(wSelf) sSelf = wSelf;
        if (sSelf) {
            [sSelf setNavigationBarHidden:viewController.isNavigationBarHidden animated:animated];
            viewController.imy_config.preferredStatusBarStyle = viewController.statusBarStyle;
        }
    };
    
    if ([viewController isKindOfClass:[IMYPublicBaseViewController class]]) {
        [(IMYPublicBaseViewController *)viewController setViewWillAppearInjectNavBarHidenBlock:viewWillAppearInjectNavBarHidenBlock];
    }
    
    if ([self.topViewController isKindOfClass:[IMYPublicBaseViewController class]]) {
        IMYPublicBaseViewController *topViewcontroller = (IMYPublicBaseViewController *)self.topViewController;
        if (!topViewcontroller.viewWillAppearInjectNavBarHidenBlock) {
            [topViewcontroller setViewWillAppearInjectNavBarHidenBlock:viewWillAppearInjectNavBarHidenBlock];
        }
    }
}

- (UITabBarItem *)tabBarItem {
    UITabBarItem *tabBarItem = nil;
    if (self.usingRootVCTabBarItem) {
        if (_wrapRootVC) {
            tabBarItem = _wrapRootVC.tabBarItem;
        } else {
            tabBarItem = self.viewControllers.firstObject.tabBarItem;
        }
    }
    if (!tabBarItem) {
        tabBarItem = [super tabBarItem];
    }
    return tabBarItem;
}

#pragma mark - wrap delegate
- (void)setDelegate:(id<UINavigationControllerDelegate>)delegate {
    [self setWrapDelegate:delegate];
}

- (void)setWrapDelegate:(id<UINavigationControllerDelegate>)delegate {
    if (_wrapDelegate != delegate) {
        _wrapDelegate = delegate;
        [super setDelegate:nil];
        [super setDelegate:self];
    }
}

- (void)navigationController:(UINavigationController *)navigationController willShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    self.canAction = NO;
    UIGestureRecognizerState state = navigationController.interactivePopGestureRecognizer.state;
    BOOL isGestureAction = (state == UIGestureRecognizerStateBegan || state == UIGestureRecognizerStateChanged);
    if (isGestureAction) {
        [self checkGestureStatus:navigationController.interactivePopGestureRecognizer];
    } else {
        [self actionDelayResetMark];
    }
    if (self.isTopItemsFixing) {
        [self checkNavigationBarItems];
    }
    if ([self.wrapDelegate respondsToSelector:@selector(navigationController:willShowViewController:animated:)]) {
        [self.wrapDelegate navigationController:navigationController willShowViewController:viewController animated:animated];
    }
}

- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    self.canAction = YES;

    [NSObject imy_cancelBlockForKey:self.actionDelayResetQueueKey];
    [self checkNavigationBarItems];

    if (self.enableFullPanGestureRecognizer) {
        BOOL enable = YES;
        self.respondsFullPanEnableSEL = [viewController respondsToSelector:@selector(enableFullPopGestureRecognizer)];
        if (self.respondsFullPanEnableSEL) {
            enable = [(id)viewController enableFullPopGestureRecognizer];
        }
        [self setCurrentPageEnableFullPanGestureRecognizer:enable];
    }

    if ([self.wrapDelegate respondsToSelector:@selector(navigationController:didShowViewController:animated:)]) {
        [self.wrapDelegate navigationController:navigationController didShowViewController:viewController animated:animated];
    }
}

- (nullable id<UIViewControllerAnimatedTransitioning>)navigationController:(UINavigationController *)navigationController
                                           animationControllerForOperation:(UINavigationControllerOperation)operation
                                                        fromViewController:(UIViewController *)fromVC
                                                          toViewController:(UIViewController *)toVC {
    if ([self.wrapDelegate respondsToSelector:@selector(navigationController:animationControllerForOperation:fromViewController:toViewController:)]) {
        return [self.wrapDelegate navigationController:navigationController animationControllerForOperation:operation fromViewController:fromVC toViewController:toVC];
    }
    return nil;
}

- (nullable id <UIViewControllerInteractiveTransitioning>)navigationController:(UINavigationController *)navigationController
                                   interactionControllerForAnimationController:(id <UIViewControllerAnimatedTransitioning>) animationController {
    if ([self.wrapDelegate respondsToSelector:@selector(navigationController:interactionControllerForAnimationController:)]) {
        return [self.wrapDelegate navigationController:navigationController interactionControllerForAnimationController:animationController];
    }
    return nil;
}

- (BOOL)respondsToSelector:(SEL)aSelector {
    BOOL hasSelector = [super respondsToSelector:aSelector];
    if (!hasSelector && self.wrapDelegate) {
        hasSelector = [self.wrapDelegate respondsToSelector:aSelector];
    }
    return hasSelector;
}

- (id)forwardingTargetForSelector:(SEL)aSelector {
    if ([self.wrapDelegate respondsToSelector:aSelector]) {
        return self.wrapDelegate;
    } else {
        return [super forwardingTargetForSelector:aSelector];
    }
}

#pragma mark - touch recognizer

- (void)checkGestureStatus:(UIGestureRecognizer *)gestureRecognizer {
    ///不用再去监听了
    if (self.canAction) {
        return;
    }
    __weak UIGestureRecognizer *weakGestureRecognizer = gestureRecognizer;
    UIGestureRecognizerState state = weakGestureRecognizer.state;
    BOOL isGestureAction = (state == UIGestureRecognizerStateBegan || state == UIGestureRecognizerStateChanged);
    if (!weakGestureRecognizer || !isGestureAction) {
        self.canAction = YES;
    } else {
        imy_asyncMainBlock(0.1, ^{
            [self checkGestureStatus:weakGestureRecognizer];
        });
    }
}

- (void)checkNavigationBarItems {
    UINavigationBar *navigationBar = self.navigationBar;
    ///navigation bar 是否隐藏
    if (navigationBar.hidden || !navigationBar.topItem || navigationBar.imy_left < 0) {
        return;
    }
    UINavigationItem *navItem = self.topViewController.navigationItem;
    ///一致则不进行修正
    if ([navigationBar.topItem isEqual:navItem]) {
        return;
    }
    ///从此每次都要进行修正
    self.isTopItemsFixing = YES;

    [navigationBar setValue:@0 forKey:@"locked"];
    NSArray *items = [self.viewControllers map:^id(UIViewController *element) {
        return element.navigationItem;
    }];
    [navigationBar setItems:items animated:NO];
    [navigationBar setValue:@1 forKey:@"locked"];
}


#pragma mark - 全屏返回手势

- (double)defaultFullPanLeftMaxBeginWidth {
    return 50;
}

- (double)defaultFullPanRightMinBeginWidth {
    static double value = 0;
    if (!value) {
        value = SCREEN_WIDTH - 50;
    }
    return value;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldReceiveTouch:(UITouch *)touch {
    if (![gestureRecognizer isEqual:self.interactivePopGestureRecognizer]) {
        return YES;
    }
    ///是否处于不能返回状态
    if (IMYSystem.isShowingKeyboardWindow || self.viewControllers.count <= 1) {
        return NO;
    }
    /// 查找不支持优化退出的视图
    UIView *disablePanView = [touch.view imy_findParentViewWithFilterBlock:^BOOL(UIView * _Nonnull view) {
        if ([view isKindOfClass:NSClassFromString(@"IMYVPlayerView")]) {
            /// 视频区域不返回
            return YES;
        }
        /// 当前响应视图存在全屏右滑冲突手势，且优先级高
        return view.imy_disableFullPan;
    }];
    if (disablePanView) {
        return NO;
    }
    ///判断手势位置  是否超过阈值
    CGPoint location = [touch locationInView:gestureRecognizer.view];
    if ([self locationX:location.x greaterThan:self.panReceiveTouchWidth]) {
        return NO;
    }
    return YES;
}

- (BOOL)locationX:(const CGFloat)x greaterThan:(const CGFloat)value {
    if (IMYISRTL) {
        return x < (SCREEN_WIDTH - value);
    } else {
        return x > value;
    }
}

- (BOOL)fullPanGestureRecognizerShouldBegin:(UIPanGestureRecognizer *)gestureRecognizer {
    CGPoint location = [gestureRecognizer locationInView:gestureRecognizer.view];
    if ([self locationX:location.x greaterThan:self.panBeginTouchWidth]) {
        return NO;
    }

    if ([self locationX:location.x greaterThan:self.defaultFullPanRightMinBeginWidth]) {
        return NO;
    }

    CGPoint velocity = [gestureRecognizer velocityInView:gestureRecognizer.view];
    if (fabs(velocity.y) > fabs(velocity.x)) {
        return NO;
    }

    return YES;
}

- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    
    UIPanGestureRecognizer *panGesture = (id)gestureRecognizer;
    // 判断是否符合侧滑的启动条件
    if (![self fullPanGestureRecognizerShouldBegin:panGesture]) {
        return NO;
    }

    id lastViewController = self.viewControllers.lastObject;
    // 每个vc自己也可以定制侧滑开启的条件
    if ([lastViewController respondsToSelector:@selector(fullPopGestureRecognizerShouldBegin:)] &&
        ![lastViewController fullPopGestureRecognizerShouldBegin:panGesture]) {
        return NO;
    }

    self.canAction = NO;
    [self checkGestureStatus:gestureRecognizer];

    return YES;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    if ([self shouldRecognizeSimultaneouslyWithGestureRecognizer:otherGestureRecognizer]) {
        return YES;
    }
    return NO;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldRequireFailureOfGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    UIPanGestureRecognizer *panGesture = (id)gestureRecognizer;

    if (![self fullPanGestureRecognizerShouldBegin:panGesture]) {
        return YES;
    }

    ///是否取消全局返回手势
    BOOL requireFailure = ![self shouldBeRequiredToFailByGestureRecognizer:otherGestureRecognizer];
    return requireFailure;
}

- (BOOL)shouldRecognizeSimultaneouslyWithGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    if ([otherGestureRecognizer isKindOfClass:[UILongPressGestureRecognizer class]]) {
        return YES;
    }

    if ([otherGestureRecognizer isKindOfClass:[UITapGestureRecognizer class]]) {
        return YES;
    }
    
    if ([otherGestureRecognizer isKindOfClass:UISwipeGestureRecognizer.class]) {
        return YES;
    }

    if ([NSStringFromClass(otherGestureRecognizer.class) containsString:@"DelayedTouchesBeganGesture"]) {
        return YES;
    }

    if ([otherGestureRecognizer.delegate isKindOfClass:NSClassFromString(@"WXComponent")]) {
        return YES;
    }

    if ([otherGestureRecognizer isKindOfClass:NSClassFromString(@"RCTTouchHandler")]) {
        return YES;
    }

    return NO;
}

- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldBeRequiredToFailByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
    if ([self shouldRecognizeSimultaneouslyWithGestureRecognizer:otherGestureRecognizer]) {
        return NO;
    }

    /// 在非全屏的视频播放控件内，不允许左滑返回
    UIView *videoView = [otherGestureRecognizer.view imy_findParentViewWithFilterBlock:^BOOL(UIView * _Nonnull view) {
        NSString *className = NSStringFromClass(view.class);
        return [className containsString:@"VideoView"] || [className containsString:@"PlayerView"];
    }];
    
    if (videoView != nil && videoView.imy_height < SCREEN_HEIGHT/2) {
        return NO;
    }
    
    ///如果有重载，则不进行 WebView 判断
    if (!self.respondsFullPanEnableSEL) {
        NSString *webClassName = NSStringFromClass(otherGestureRecognizer.view.class);
        BOOL hasWebView = [webClassName containsString:@"UIWeb"] || [webClassName containsString:@"WKWeb"];
        if (hasWebView) {
            CGPoint location = [((UIPanGestureRecognizer *)gestureRecognizer) locationInView:gestureRecognizer.view];
            if ([self locationX:location.x greaterThan:self.defaultFullPanLeftMaxBeginWidth]) {
                return NO;
            }
        }
    }
    ///是否取消其他手势
    BOOL shouldToFail = [self shouldBeRequiredToFailByGestureRecognizer:otherGestureRecognizer];
    return shouldToFail;
}

- (BOOL)shouldBeRequiredToFailByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {

    UIView *inputView = [otherGestureRecognizer.view imy_findParentViewWithClasses:@[UITextField.class, UITextView.class]];
    if (inputView.isFirstResponder) {
        // 输入框属于焦点状态，则不执行返回手势
        return NO;
    }
    
    // 左右滑动手势
    if ([otherGestureRecognizer isKindOfClass:UISwipeGestureRecognizer.class]) {
        UISwipeGestureRecognizerDirection const swipeDirection = [(UISwipeGestureRecognizer *)otherGestureRecognizer direction];
        if (swipeDirection == UISwipeGestureRecognizerDirectionLeft ||
            swipeDirection == UISwipeGestureRecognizerDirectionRight) {
            return NO;
        }
    }
    
    // 寻找可左右滚动的scrollView
    BOOL shouldBegin = YES;
    UIScrollView *scrollView = [[otherGestureRecognizer view] imy_findParentViewWithClass:[UIScrollView class]];
    while (scrollView && scrollView.contentSize.width <= SCREEN_WIDTH) {
        scrollView = [scrollView.superview imy_findParentViewWithClass:[UIScrollView class]];
    }
    
    // 可滚动的scrollview
    if (scrollView.contentSize.width > scrollView.imy_width) {
        // 判断返回手势 是否在边缘
        UIPanGestureRecognizer *panGestureRecognizer = (id)self.interactivePopGestureRecognizer;
        CGPoint location = [panGestureRecognizer locationInView:panGestureRecognizer.view];
        if ([self locationX:location.x greaterThan:self.defaultFullPanLeftMaxBeginWidth]) {
            shouldBegin = NO;
        }
        // vc返回值优先级最高
        if (!shouldBegin) {
            id lastViewController = self.viewControllers.lastObject;
            if ([lastViewController respondsToSelector:@selector(fullPopGestureRecognizer:shouldBeginByScrollView:)]) {
                shouldBegin = [lastViewController fullPopGestureRecognizer:(id)self.interactivePopGestureRecognizer shouldBeginByScrollView:scrollView];
            }
        }
    }
    return shouldBegin;
}

#pragma mark - InterfaceOrientation

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    const UIInterfaceOrientationMask mask = self.topViewController.supportedInterfaceOrientations;
    if (mask == UIInterfaceOrientationUnknown) {
        // 可能VC不见了，返回默认竖屏
        return UIInterfaceOrientationMaskPortrait;
    }
    return mask;
}

- (BOOL)shouldAutorotate {
    return self.topViewController.shouldAutorotate;
}

- (BOOL)shouldAutorotateToInterfaceOrientation:(UIInterfaceOrientation)interfaceOrientation {
    return [self.topViewController shouldAutorotateToInterfaceOrientation:interfaceOrientation];
}

- (UIInterfaceOrientation)preferredInterfaceOrientationForPresentation {
    const UIInterfaceOrientation interfaceOrientation = self.topViewController.preferredInterfaceOrientationForPresentation;
    if (interfaceOrientation == UIInterfaceOrientationUnknown) {
        // 可能VC不见了，返回默认竖屏
        return UIInterfaceOrientationPortrait;
    }
    return interfaceOrientation;
}

@end

@implementation UIScreenEdgePanGestureRecognizer (IMYSEFull)

IMY_KYLIN_FUNC_METHOD_SWIZZLE {
    [UIScreenEdgePanGestureRecognizer imy_swizzleMethod:@selector(initWithTarget:action:) withMethod:@selector(imyse_initWithTarget:action:) error:nil];
}

- (instancetype)imyse_initWithTarget:(id)target action:(SEL)action {
    NSString *methodName = NSStringFromSelector(action);
    if ([methodName hasSuffix:@"gationTransition:"] && [methodName hasPrefix:@"handleNavi"]) {
        IMYKVCHashMap *hashMap = self.imykvc_hashMap;
        hashMap.target = target;
        hashMap.action = action;
    }
    return [self imyse_initWithTarget:target action:action];
}

@end


@implementation UIPercentDrivenInteractiveTransition (IMYSEFull)

IMY_KYLIN_FUNC_METHOD_SWIZZLE {
    [UIPercentDrivenInteractiveTransition imy_swizzleMethod:@selector(percentComplete) withMethod:@selector(imy_percentComplete) error:nil];
}

- (CGFloat)imy_percentComplete {
    // 防止递归
    // by bugly: https://bugly.qq.com/v2/crash-reporting/crashes/900019699/26845454?pid=2
    static BOOL hasCalling = NO;
    if (hasCalling) {
        return 1;
    }
    hasCalling = YES;
    CGFloat percentComplete = [self imy_percentComplete];
    if (![self isMemberOfClass:UIPercentDrivenInteractiveTransition.class]) {
        /// 交互改为1/4处就pop update  7.9.5
        if (percentComplete > 0.25) {
            self.completionSpeed = 1 - percentComplete;
            percentComplete = 1;
            [self finishInteractiveTransition];
        }
    }
    hasCalling = NO;
    return percentComplete;
}

@end

@implementation IMYFullScreenPanGestureRecognizer

- (void)touchesEnded:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event {
    // 判断 View 是否有位移，无位移，则取消该返回手势
    if ([self needCancelledTouches]) {
        [super touchesCancelled:touches withEvent:event];
    } else {
        [super touchesEnded:touches withEvent:event];
    }
}

- (BOOL)needCancelledTouches {
    UIPercentDrivenInteractiveTransition *transitioning = self.currentPanTarget;
    if ([transitioning isKindOfClass:UIPercentDrivenInteractiveTransition.class] && transitioning.percentComplete == 0) {
        return YES;
    }
    return NO;
}

@end
