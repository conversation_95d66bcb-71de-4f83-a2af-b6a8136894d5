//
//  IMYURIManager.m
//  IMY_ViewKit
//
//  Created by ljh on 15/7/14.
//  Copyright (c) 2015年 IMY. All rights reserved.
//

#import "IMYURIManager.h"
#import "IMYPublic.h"
#import <pthread.h>

#ifdef DEBUG
static NSMutableArray<NSString *> *_currentRunningUriPaths;
#endif

#pragma mark - IMYURIBlockObject
@interface IMYURIBlockObject : NSObject
@property (nonatomic, copy) NSString *path;
@property (nonatomic, copy, readonly) NSArray *keyComponent;
@property (nonatomic, assign) NSInteger level;
@property (nonatomic, assign) BOOL wildcard;
@property (nonatomic, assign) <PERSON><PERSON>OL allmatch;
@property (nonatomic, copy) void (^actionBlock)(IMYURIActionBlockObject *);
@end

@implementation IMYURIBlockObject
@synthesize keyComponent = _keyComponent;

- (void)setPath:(NSString *)path {
    NSCharacterSet *characterSet = [NSCharacterSet characterSetWithCharactersInString:@"/"];
    _path = [path.lowercaseString stringByTrimmingCharactersInSet:characterSet];
    _keyComponent = [_path componentsSeparatedByCharactersInSet:characterSet];
}

- (NSString *)description {
    return [NSString stringWithFormat:@"<IMYURIBlockObject: %p, path: %@, level: %ld>", self, _path, (long)_level];
}

@end

#pragma mark - IMYURIManager

@interface IMYURIManager () {
    pthread_mutex_t _lock;
    BOOL _needSorted;
}

@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray *> *uriCache;
@property (nonatomic, strong) NSMutableArray<IMYURIBlockObject *> *pathBlockArray;
@property (nonatomic, strong) NSArray<NSString *> *urlSchemes;

@end

@implementation IMYURIManager

+ (instancetype)sharedInstance {
    static id instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [super new];
    });
    return instance;
}

+ (instancetype)shareURIManager {
    return [self sharedInstance];
}

- (instancetype)init {
    self = [super init];
    if (self) {
        pthread_mutex_init(&_lock, NULL);

        _uriCache = [NSMutableDictionary dictionary];
        _pathBlockArray = [NSMutableArray array];

        NSDictionary *dic = [[NSBundle mainBundle] infoDictionary];
        NSArray *array = [dic objectForKey:@"CFBundleURLTypes"];

        NSMutableArray *urlSchemes = [NSMutableArray array];
        for (NSDictionary *urlTypes in array) {
            NSArray *CFBundleURLSchemes = urlTypes[@"CFBundleURLSchemes"];
            if ([CFBundleURLSchemes isKindOfClass:[NSArray class]]) {
                [urlSchemes addObjectsFromArray:CFBundleURLSchemes];
            }
        }
        _urlSchemes = urlSchemes;
    }
    return self;
}

- (BOOL)containScheme:(NSString *)scheme {
    if ([scheme isEqualToString:@"meiyou"] || [scheme isEqualToString:@"meetyou"] || [_urlSchemes containsObject:scheme]) {
        return YES;
    }
    return NO;
}

- (BOOL)containActionBlockForPath:(NSString *)uriPath {
    return [self hitURIBlocksForPath:uriPath].count > 0;
}

- (NSArray<IMYURIBlockObject *> *)hitURIBlocksForPath:(NSString *)path {
    NSCharacterSet *characterSet = [NSCharacterSet characterSetWithCharactersInString:@"/"];
    NSString *uriPath = [path.lowercaseString stringByTrimmingCharactersInSet:characterSet];
    pthread_mutex_lock(&_lock);
    // 先搜索缓存
    NSArray<IMYURIBlockObject *> *hitURIBlocks = _uriCache[uriPath];
    if (!hitURIBlocks) {
        // 需要对 action block 进行一次排序
        if (_needSorted) {
            [_pathBlockArray sortUsingComparator:^NSComparisonResult(IMYURIBlockObject *obj1, IMYURIBlockObject *obj2) {
                // level 低的在前，相同等级有通配符的放在最后
                if (obj1.level > obj2.level) {
                    return NSOrderedDescending;
                } else if (obj1.level < obj2.level) {
                    return NSOrderedAscending;
                } else if (obj1.wildcard && !obj2.wildcard) {
                    return NSOrderedDescending;
                } else if (!obj1.wildcard && obj2.wildcard) {
                    return NSOrderedAscending;
                }
                return NSOrderedSame;
            }];
            _needSorted = NO;
        }
        // 命中的 URI Block
        NSMutableArray<IMYURIBlockObject *> *hitURIBlockArray = [NSMutableArray array];
        // path component
        NSArray *uriPathComponent = [uriPath componentsSeparatedByCharactersInSet:characterSet];
        // 进行大循环找出所有命中的 URI Block
        for (IMYURIBlockObject *blockObject in _pathBlockArray) {
            // 全部匹配
            if (blockObject.allmatch) {
                [hitURIBlockArray addObject:blockObject];
                continue;
            }
            // 路径直接匹配
            if ([blockObject.path isEqualToString:uriPath]) {
                [hitURIBlockArray addObject:blockObject];
                continue;
            }
            NSArray *keyComponent = blockObject.keyComponent;
            // 直接跳过 分段数量不相等 的匹配
            if (uriPathComponent.count != keyComponent.count) {
                continue;
            }
            // 默认匹配为YES
            BOOL matching = YES;
            NSInteger idx = 0;
            for (NSString *keyCompName in keyComponent) {
                if ([keyCompName isEqualToString:@"*"]) {
                    // 该key分段 是通配符
                    idx ++;
                    continue;;
                }
                NSString *pathCompName = uriPathComponent[idx];
                if ([keyCompName isEqualToString:pathCompName]) {
                    // 该key分段相等
                    idx ++;
                    continue;
                }
                // 匹配不通过
                matching = NO;
                break;
            }
            // 找到可以匹配的block
            if (matching) {
                [hitURIBlockArray addObject:blockObject];
            }
        }
        hitURIBlocks = [hitURIBlockArray copy];
        _uriCache[uriPath] = hitURIBlocks;
    }
    pthread_mutex_unlock(&_lock);
    return hitURIBlocks;
}

@end

@implementation IMYURIManager (Register)

- (void)addForPath:(NSString *)path withActionBlock:(void (^)(IMYURIActionBlockObject *actionObject))actionBlock {
    [self addForPath:path level:0 withActionBlock:actionBlock];
}

- (void)addForPath:(NSString *)path level:(NSInteger)level withActionBlock:(void (^)(IMYURIActionBlockObject *actionObject))actionBlock {
    if (!actionBlock || !path) {
        NSAssert(NO, @"URI block params is nil!");
        return;
    }
    pthread_mutex_lock(&_lock);

    IMYURIBlockObject *map = [IMYURIBlockObject new];
    map.path = path;
    map.actionBlock = actionBlock;
    if (level < -9999 || level > 9999) {
        NSAssert(NO, @"URI的优先级要在 [-9999 ~ 9999] 之间");
        level = 0;
    }
    map.level = level;
    // 是否带有通配符（带通配符同等优先级情况下，优先级更低）
    map.wildcard = [path containsString:@"*"];
    // 全部匹配
    map.allmatch = [path isEqualToString:@"****"];

    [_pathBlockArray addObject:map];
    _needSorted = YES;
    [_uriCache removeAllObjects];
    
    pthread_mutex_unlock(&_lock);
}

@end

@implementation IMYURIManager (Deprecated)

- (void)addPathActionBlock:(void (^)(IMYURIActionBlockObject *))actionBlock forPath:(NSString *)path {
    [self addPathActionBlock:actionBlock forPath:path level:0];
}

- (void)addPathActionBlock:(void (^)(IMYURIActionBlockObject *))actionBlock forPath:(NSString *)path level:(NSInteger)level {
    [self addForPath:path level:level withActionBlock:actionBlock];
}

@end

@implementation IMYURIManager (Running)

- (BOOL)runActionWithPath:(NSString *)path params:(NSDictionary *)params info:(NSDictionary *)info {
    return [self runActionWithURI:[IMYURI uriWithPath:path params:params info:info] completed:nil];
}

- (BOOL)runActionWithString:(NSString *)uri {
    return [self runActionWithURI:[IMYURI uriWithURIString:uri] completed:nil];
}

- (BOOL)runActionWithURI:(IMYURI *)uri {
    return [self runActionWithURI:uri completed:nil];
}

- (BOOL)runActionWithString:(NSString *)uri completed:(void (^)(IMYURIActionBlockObject *))completedBlock {
    return [self runActionWithURI:[IMYURI uriWithURIString:uri] completed:completedBlock];
}

- (BOOL)runActionWithURI:(IMYURI *)uri completed:(void (^)(IMYURIActionBlockObject *))completedBlock {
    return [self runActionWithActionObject:[IMYURIActionBlockObject actionBlockWithURI:uri] completed:completedBlock];
}

- (id)runActionAndSyncResultWithPath:(NSString *)path params:(NSDictionary *)params {
    __block id returnObject = nil;
    [self runActionAndSyncResultWithPath:path params:params callbackBlock:^(id result, NSError *error, NSString *eventName) {
        returnObject = result;
    }];
    return returnObject;;
}

- (BOOL)runActionAndSyncResultWithPath:(NSString *)path params:(NSDictionary *)params callbackBlock:(void (^)(id, NSError *, NSString *))callbackBlock {
    IMYURI *uri = [IMYURI uriWithPath:path params:params info:nil];
    IMYURIActionBlockObject *actionObject = [IMYURIActionBlockObject actionBlockWithURI:uri];
    actionObject.implCallbackBlock = callbackBlock;
    return [self runActionWithActionObject:actionObject completed:nil];
}

- (BOOL)runActionWithActionObject:(IMYURIActionBlockObject *)actionObject completed:(void (^)(IMYURIActionBlockObject *))completedBlock {
    IMYURI *uri = actionObject.uri;
    actionObject.hasStop = NO;
    
    // 判断是否需要替换 URI 协议
    IMYURI *freedomURI = nil;
    do {
        // 防止无限递归，已经替换的 URI， 不在执行替换判断
        if ([uri.info[@"has_uri_replace"] boolValue]) {
            break;
        }
        IMYABTestVariables *vars = [[IMYCommonConfig sharedInstance] configForKey:@"uri_replace"];
        NSDictionary *configs = [vars dictionaryForKey:uri.path];
        if (!configs.count) {
            // 无相关配置
            break;
        }
        // 3个变量互斥，只能执行一个
        NSString *full = configs[@"full"];
        if (full.length > 0) {
            // 完整替换
            freedomURI = [IMYURI uriWithURIString:full];
            break;
        }
        NSString *path = configs[@"path"];
        if (path.length > 0) {
            // path 替换，参数不变
            freedomURI = [IMYURI uriWithScheme:uri.scheme path:path params:uri.params info:uri.info];
            break;
        }
        NSString *replace = configs[@"replace"];
        if (replace.length > 0) {
            // 追加替换，原有参数追加到 新的URI中
            freedomURI = [IMYURI uriWithURIString:replace];
            [freedomURI appendingParams:uri.params];
            break;
        }
    } while (0);
    
    // 执行新的 URI协议
    if (freedomURI != nil) {
        [freedomURI appendingInfo:@{@"has_uri_replace" : @YES}];
        [self runActionWithURI:freedomURI completed:completedBlock];
        return YES;
    }
    
    if (![self containScheme:uri.scheme]) {
        ///不是内部的协议
        [[UIApplication sharedApplication] openURL:[NSURL URLWithString:uri.uri]];
        if (completedBlock) {
            completedBlock(actionObject);
        }
        return NO;
    }

    if (imy_isBlankString(uri.path)) {
#if IS_TEST
        if (isSimulator && [uri.scheme hasPrefix:@"me"]) {
            NSAssert(NO, @"URI Path 为空");
        }
#endif
        if (completedBlock) {
            completedBlock(actionObject);
        }
        return NO;
    }
    
    UIViewController *currentVC = nil;
    if ([NSThread isMainThread]) {
        currentVC = actionObject.getUsingViewController;
    }
    
#ifdef DEBUG
    // push
    if (NSThread.isMainThread) {
        if (!_currentRunningUriPaths) {
            _currentRunningUriPaths = [NSMutableArray array];
        }
        [_currentRunningUriPaths addObject:uri.path];
    }
#endif
    
    BOOL hasAction = [self runPathBlockActionWithActionObject:actionObject];
    if (!hasAction) {
        hasAction = [self runViewControllerMapActionWithActionObject:actionObject];
    }

#ifdef DEBUG
    // pop
    if (NSThread.isMainThread) {
        [_currentRunningUriPaths removeLastObject];
    }
#endif
    
    if (hasAction && uri.info.count > 0) {
        NSDictionary *infoMap = uri.info;
        BOOL clear_last = [[infoMap objectForKey:@"clear_last"] boolValue];
        BOOL clear_all = [[infoMap objectForKey:@"clear_all"] boolValue];
        NSInteger clear_count = [[infoMap objectForKey:@"clear_count"] integerValue];
        if (clear_last) {
            [currentVC imy_removeSelfInNavigationController];
        } else if (clear_all) {
            [currentVC imy_removeTopBeforeCountInNavigationController:10000];
        } else if (clear_count > 0) {
            [currentVC imy_removeTopBeforeCountInNavigationController:clear_count];
        }
    }

    if (completedBlock) {
        completedBlock(actionObject);
    }

    return hasAction;
}

- (BOOL)runPathBlockActionWithActionObject:(IMYURIActionBlockObject *)actionObject {
    ///先获取全部 ActionBlock
    NSArray<IMYURIBlockObject *> *hitURIBlocks = [self hitURIBlocksForPath:actionObject.uri.path];
    ///执行 URI Block
    for (IMYURIBlockObject *blockObject in hitURIBlocks) {
        actionObject.hasStop = YES;
        blockObject.actionBlock(actionObject);
        if (actionObject.hasStop) {
            break;
        }
    }
    ///uri 是否被 block 拦截
    return actionObject.hasStop;
}

- (BOOL)runViewControllerMapActionWithActionObject:(IMYURIActionBlockObject *)actionObject {
    IMYURI * const uri = actionObject.uri;

    Class vcClass = nil;
    if (uri.path.length > 0) {
        vcClass = NSClassFromString(uri.path);
    }
    
    if (!vcClass || ![vcClass isSubclassOfClass:UIViewController.class]) {
        NSLog(@"没找到对应的VC : %@", uri.uri);
        return NO;
    }

    if (![NSThread isMainThread]) {
        // 在异步线程 跳转 VC
        NSAssert(NO, @"在异步线程 跳转 VC : %@", uri.uri);
        return NO;
    }
    
    UIViewController *viewController = [vcClass new];
    [viewController imy_setPropertyWithDictionary:uri.params];

    BOOL animated = YES;
    BOOL present = NO;

    if (uri.info[@"animated"]) {
        animated = [uri.info[@"animated"] boolValue];
    }
    if (uri.info[@"present"]) {
        present = [uri.info[@"present"] boolValue];
    }

    UIViewController *currentVC = actionObject.getUsingViewController;
    if (present) {
        [currentVC imy_present:viewController animated:animated];
    } else {
        [currentVC imy_push:viewController animated:animated];
    }
    actionObject.hasStop = YES;
    
    return YES;
}

@end


@implementation IMYURIManager (UniversalLinks)

- (BOOL)runActionWithWebPageURLString:(NSString *)urlString {
    if (imy_isBlankString(urlString)) {
        return NO;
    }
    NSDictionary *queryDictionary = urlString.imy_queryDictionary;
    NSString *actionURL = [queryDictionary objectForKey:@"link"];
    if (imy_isBlankString(actionURL)) {
        actionURL = nil;
    }
    NSString *actionScheme = nil;
    if ([actionURL containsString:@"://"]) {
        actionScheme = [actionURL componentsSeparatedByString:@"://"].firstObject;
    }
    NSString *webURL = [queryDictionary objectForKey:@"weblink"];
    if (!webURL) {
        webURL = urlString;
    }
    IMYURI *actionURI = nil;
    if ([self containScheme:actionScheme]) {
        actionURI = [IMYURI uriWithURIString:actionURL];
    }
    [[IMYPublicAppHelper shareAppHelper] runActionBlockWithLaunchFinished:^{
        BOOL hasAction = NO;
        if (actionURI) {
            hasAction = [self runActionWithURI:actionURI];
        }
        if (!hasAction) {
            IMYURI *webURI = [IMYURI uriWithPath:@"web" params:@{@"url": webURL} info:nil];
            [self runActionWithURI:webURI];
        }
    } forKey:@"runActionWithWebPageURLString"];
    return YES;
}

- (BOOL)runActionWithUserActivity:(NSUserActivity *)userActivity {
    if (![userActivity.activityType isEqualToString:NSUserActivityTypeBrowsingWeb]) {
        return NO;
    }
    NSString *webpageURLString = userActivity.webpageURL.absoluteString;
    return [self runActionWithWebPageURLString:webpageURLString];
}

@end

#ifdef DEBUG

@implementation UINavigationController (CurrentRunningUriPath)

- (void)imyurix_pushViewController:(UIViewController *)viewController animated:(BOOL)animated {
    NSString *uriPath = _currentRunningUriPaths.lastObject;
    id fromURI = [viewController respondsToSelector:@selector(fromURI)] ? [(id)viewController fromURI] : @1;
    // 正在执行 URI协议， 但是跳转的VC 没有赋值 fromURI
    if (uriPath.length > 0 && !fromURI) {
        NSString *warning = [NSString stringWithFormat:@"debug-log: %@，%@ 没有赋值fromURI", uriPath, [(id)viewController ga_pageName]];
        [UIWindow imy_showTextHUD:warning];
        NSLog(@"\n%@\n", warning);
    }
    [self imyurix_pushViewController:viewController animated:animated];
}

@end

@implementation UIViewController (CurrentRunningUriPath)

- (void)imyurix_presentViewController:(UIViewController *)viewControllerToPresent animated:(BOOL)flag completion:(void (^)(void))completion {
    NSString *uriPath = _currentRunningUriPaths.lastObject;
    id fromURI = [viewControllerToPresent respondsToSelector:@selector(fromURI)] ? [(id)viewControllerToPresent fromURI] : @1;
    // 正在执行 URI协议， 但是跳转的VC 没有赋值 fromURI
    if (uriPath.length > 0 && !fromURI) {
        NSString *warning = [NSString stringWithFormat:@"debug-log: %@，%@ 没有赋值fromURI", uriPath, [(id)viewControllerToPresent ga_pageName]];
        [UIWindow imy_showTextHUD:warning];
        NSLog(@"\n%@\n", warning);
    }
    [self imyurix_presentViewController:viewControllerToPresent animated:flag completion:completion];
}

@end

IMY_KYLIN_FUNC_METHOD_SWIZZLE {
    [UIViewController imy_swizzleMethod:@selector(presentViewController:animated:completion:)
                             withMethod:@selector(imyurix_presentViewController:animated:completion:)
                                  error:nil];
    [UINavigationController imy_swizzleMethod:@selector(pushViewController:animated:)
                                   withMethod:@selector(imyurix_pushViewController:animated:)
                                        error:nil];
}

#endif

